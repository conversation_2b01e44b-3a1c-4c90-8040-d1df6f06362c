FROM public.ecr.aws/lambda/python:3.11

# Install system packages and Python dependencies in a single layer
RUN yum install -y gcc gcc-c++ make swig wget git && yum clean all

# Copy requirements and install Python packages
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    rm requirements.txt

# Copy app code
COPY app/ ${LAMBDA_TASK_ROOT}/

# AWS Lambda entry point
CMD ["main.handler"]