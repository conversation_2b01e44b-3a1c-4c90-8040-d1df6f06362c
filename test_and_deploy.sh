#!/bin/bash

set -e

# Source environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | xargs)
    echo "✅ Loaded environment variables from .env"
else
    echo "❌ .env file not found"
    exit 1
fi

# Load NVM and Node.js
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
echo "✅ Loaded Node.js $(node --version)"

echo "🔁 Rebuilding Docker image..."
docker build -t ke-faiss-api .

echo "🔍 Validating contents inside container..."
docker run --rm --entrypoint="" ke-faiss-api sh -c "ls -lh onenote_chunks.json faiss.index faiss_metadata.json || echo '❌ Some files missing'"

echo "🧪 Running local Lambda simulation with AWS credentials..."
docker run --rm -d \
  -p 9000:8080 \
  -e OPENAI_API_KEY=$OPENAI_API_KEY \
  -e AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID \
  -e AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY \
  -e AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION \
  --name ke-faiss-local \
  ke-faiss-api

echo "⏳ Waiting for Lambda to initialize (S3 download takes longer)..."
sleep 10

echo "🏥 Testing health endpoint..."
curl -s -XPOST "http://localhost:9000/2015-03-31/functions/function/invocations" \
  -H "Content-Type: application/json" \
  -d '{
    "version": "2.0",
    "routeKey": "GET /health",
    "rawPath": "/health",
    "rawQueryString": "",
    "headers": {},
    "requestContext": {
      "http": {
        "method": "GET",
        "path": "/health",
        "protocol": "HTTP/1.1",
        "sourceIp": "127.0.0.1"
      }
    },
    "body": "",
    "isBase64Encoded": false
  }' | jq .

echo "📤 Sending test query: 'What is hybrid search and which project have we used it for?'"
echo "Expected answer: Should mention RFP Navigator if using latest S3 embeddings"
curl -s -XPOST "http://localhost:9000/2015-03-31/functions/function/invocations" \
  -H "Content-Type: application/json" \
  -d '{
    "version": "2.0",
    "routeKey": "POST /search",
    "rawPath": "/search",
    "rawQueryString": "",
    "headers": {
      "content-type": "application/json"
    },
    "requestContext": {
      "http": {
        "method": "POST",
        "path": "/search",
        "protocol": "HTTP/1.1",
        "sourceIp": "127.0.0.1"
      }
    },
    "body": "{\"query\":\"What is hybrid search and which project have we used it for?\"}",
    "isBase64Encoded": false
  }' | jq .

echo "🧼 Stopping local container..."
docker stop ke-faiss-local

echo "🚀 Redeploying to AWS via CDK..."
# We're already in the CDK project directory
OPENAI_API_KEY=$OPENAI_API_KEY npx cdk deploy