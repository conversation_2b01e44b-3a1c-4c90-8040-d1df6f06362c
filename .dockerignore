# CDK
cdk.out/
lib/
bin/
node_modules/
*.ts
*.js
*.d.ts
package*.json
tsconfig.json

# Git
.git/
.gitignore

# Documentation
README.md
*.md

# Scripts
*.sh
configure_aws.sh
deploy.sh
test_and_deploy.sh

# Environment
.env
.env.*

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.vscode/
.idea/
*.swp
*.swo

# macOS
.DS_Store

# Testing
test/
tests/
.coverage
.pytest_cache/

# AWS
aws/
