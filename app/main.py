from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import json
import os
import boto3
import logging
import time
from pathlib import Path
from typing import Optional, List, Dict, Any
from mangum import Mangum  # ✅ AWS Lambda adapter

# === CONFIG ===
S3_BUCKET = "knowledgebot-faiss-embedding"
S3_BASE_PREFIX = "rag-knowledge"
CACHE_DIR = "/tmp"

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True

# === S3 FUNCTIONS ===
def get_s3_client():
    """Initialize S3 client with AWS credentials from Lambda execution role"""
    try:
        return boto3.client('s3')
    except Exception as e:
        logger.error(f"Failed to initialize S3 client: {e}")
        return None

def test_s3_connection():
    """Test S3 connection and bucket access"""
    try:
        s3_client = get_s3_client()
        if not s3_client:
            return False, "Failed to initialize S3 client"
        
        # Try to list objects in the bucket
        response = s3_client.list_objects_v2(
            Bucket=S3_BUCKET,
            Prefix=S3_BASE_PREFIX,
            MaxKeys=1
        )
        return True, "S3 connection successful"
    except Exception as e:
        logger.error(f"S3 connection test failed: {e}")
        return False, str(e)

# === INIT ===
init_start = time.time()
logger.info("🚀 Starting simplified FastAPI application...")

# Test S3 connection
s3_connected, s3_message = test_s3_connection()
logger.info(f"S3 Status: {s3_message}")

total_init_time = time.time() - init_start
logger.info(f"✅ Application ready! Total startup time: {total_init_time:.3f}s")

# === FASTAPI SETUP ===
app = FastAPI(title="KE FAISS API - Simplified", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {
        "status": "ok", 
        "message": "FastAPI is running!",
        "version": "simplified",
        "s3_connected": s3_connected
    }

@app.get("/health")
def health():
    """Health check endpoint with S3 connection status"""
    s3_status, s3_msg = test_s3_connection()
    
    return {
        "status": "ok",
        "s3_connection": "connected" if s3_status else "failed",
        "s3_message": s3_msg,
        "bucket": S3_BUCKET,
        "cache_directory": CACHE_DIR,
        "version": "simplified-fastapi"
    }

@app.post("/search")
def search_endpoint(request: SearchRequest):
    """Simplified search endpoint - will be enhanced with FAISS later"""
    try:
        logger.info(f"Search request: {request.query}")
        
        # For now, return a mock response
        mock_results = [
            {
                "notebook": "Sample",
                "section": "Test",
                "page": "Demo Page",
                "text": f"This is a mock result for query: {request.query}",
                "score": 0.95
            }
        ]
        
        response = {
            "query": request.query,
            "results": mock_results[:request.top_k],
            "message": "This is a simplified version. FAISS search will be added later.",
            "total_results": len(mock_results),
            "version": "simplified"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Search error: {e}")
        return {
            "error": str(e),
            "query": request.query,
            "message": "Search failed in simplified version"
        }

@app.post("/refresh")
def refresh_cache():
    """Cache refresh endpoint - will be enhanced later"""
    return {
        "status": "ok",
        "message": "Cache refresh endpoint ready (simplified version)",
        "timestamp": time.time()
    }

# ✅ Lambda entrypoint
handler = Mangum(app)

# Optional: local run
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)