from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from openai import OpenAI
import faiss
import numpy as np
import json
import os
from mangum import Mangum  # ✅ AWS Lambda adapter

# === CONFIG ===
EMBEDDING_MODEL = "text-embedding-3-small"
INDEX_FILE = "faiss.index"
METADATA_FILE = "faiss_metadata.json"
CHUNKS_FILE = "onenote_chunks.json"

# === REQUEST BODY MODEL ===
class SearchRequest(BaseModel):
    query: str
    top_k: int = 5
    generate_answer: bool = True

# === INIT OPENAI & LOAD DATA ===
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise RuntimeError("OPENAI_API_KEY is not set in environment")

client = OpenAI(api_key=api_key)

try:
    index = faiss.read_index(INDEX_FILE)
except Exception as e:
    raise RuntimeError(f"Failed to load FAISS index: {e}")

try:
    with open(METADATA_FILE, "r", encoding="utf-8") as f:
        metadata = json.load(f)
except Exception as e:
    raise RuntimeError(f"Failed to load metadata: {e}")

try:
    with open(CHUNKS_FILE, "r", encoding="utf-8") as f:
        full_chunks = json.load(f)
except Exception as e:
    raise RuntimeError(f"Failed to load chunks: {e}")

# === FASTAPI SETUP ===
app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"status": "ok"}

@app.post("/search")
def search(req: SearchRequest):
    response = client.embeddings.create(
        input=req.query,
        model=EMBEDDING_MODEL
    )
    query_vector = np.array([response.data[0].embedding], dtype="float32")

    D, I = index.search(query_vector, req.top_k)

    results = []
    context_chunks = []

    for idx in I[0]:
        if idx < len(full_chunks):
            chunk = full_chunks[idx]
            meta = chunk.get("metadata", {})
            result = {
                "notebook": meta.get("notebook", "Unknown Notebook"),
                "section": meta.get("section", "Unknown Section"),
                "page": meta.get("page", "Unknown Page"),
                "page_id": meta.get("page_id"),  # Optional internal use
                "page_url": meta.get("page_url"),
                "chunk_index": meta.get("chunk_index", idx),
                "source_path": f"{meta.get('notebook', '?')} / {meta.get('section', '?')} / {meta.get('page', '?')}",
                "text": chunk.get("text", ""),
                "text_snippet": chunk.get("text", "")[:200] + "…"  # Optional quick view
            }
            context_chunks.append(result["text"])
            results.append(result)

    final_answer = None
    if req.generate_answer:
        joined_context = "\n\n".join(context_chunks)
        chat = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Only use the provided context to answer. Do not add external knowledge."
                },
                {
                    "role": "user",
                    "content": f"Context:\n\n{joined_context}\n\nAnswer this question:\n{req.query}"
                }
            ]
        )
        final_answer = chat.choices[0].message.content

    return {
        "query": req.query,
        "results": results,
        "answer": final_answer
    }

# ✅ Lambda entrypoint
handler = Mangum(app)

# Optional: local run
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)