# S3 Dynamic Embeddings Integration

This document describes the S3 integration for dynamic embeddings in the FAISS API.

## Overview

The application now dynamically downloads FAISS embeddings, metadata, and chunks from S3 instead of using hardcoded local files. This allows for real-time updates to the knowledge base without redeploying the Lambda function.

## S3 Bucket Structure

```
s3://knowledgebot-faiss-embedding/
├── rag-knowledge/
│   ├── latest.json                    # Points to current timestamp folder
│   ├── 2025-01-15/
│   │   ├── faiss.index
│   │   ├── faiss_metadata.json
│   │   └── onenote_chunks.json
│   └── 2025-01-16/
│       ├── faiss.index
│       ├── faiss_metadata.json
│       └── onenote_chunks.json
```

## latest.json Format

The `latest.json` file should contain:

```json
{
  "timestamp": "2025-01-16",
  "created_at": "2025-01-16T10:30:00Z",
  "description": "Latest FAISS embeddings and metadata",
  "files": {
    "faiss_index": "rag-knowledge/2025-01-16/faiss.index",
    "metadata": "rag-knowledge/2025-01-16/faiss_metadata.json",
    "chunks": "rag-knowledge/2025-01-16/onenote_chunks.json"
  }
}
```

## How It Works

1. **Initialization**: On Lambda startup, the application:
   - Downloads `latest.json` from S3
   - Extracts the current timestamp folder
   - Downloads the three required files to `/tmp/` cache
   - Falls back to local files if S3 download fails

2. **Caching**: Files are cached in `/tmp/` with a 24-hour TTL
   - Cached files are used if they exist and are recent
   - Only downloads from S3 if cache is stale or missing

3. **Fallback Strategy**:
   - Primary: S3 download to `/tmp/` cache
   - Secondary: Use existing cached files
   - Tertiary: Use local files (original behavior)

## New API Endpoints

### GET /health
Returns health information including:
- S3 connection status
- Current file sources (cached/local/not found)
- Bucket name and cache directory

### POST /refresh
Forces a refresh of embeddings from S3:
- Clears the cache
- Re-downloads from S3
- Useful for testing and manual updates

## AWS Permissions Required

The Lambda execution role needs these S3 permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject"
      ],
      "Resource": [
        "arn:aws:s3:::knowledgebot-faiss-embedding/rag-knowledge/*"
      ]
    }
  ]
}
```

## Environment Variables

- `OPENAI_API_KEY`: Required for embeddings and chat completion
- AWS credentials are automatically provided by Lambda execution role

## Logging

The application logs all S3 operations at INFO level:
- S3 connection attempts
- File download operations
- Cache hits/misses
- Fallback usage
- Error conditions

## Error Handling

- Network failures: Falls back to cached or local files
- S3 access denied: Falls back to cached or local files
- Malformed latest.json: Logs error and uses fallback
- File corruption: Attempts fallback, throws error if all sources fail

## Performance Considerations

- Files are only downloaded once per Lambda container lifecycle (unless cache expires)
- Large FAISS index files (2-3MB) are cached to minimize S3 API calls
- Cache expiry is set to 24 hours by default
- Cold start time may increase slightly due to S3 downloads

## Testing

Use the `/health` endpoint to verify:
- S3 connectivity
- Current file sources
- Cache status

Use the `/refresh` endpoint to:
- Test S3 download functionality
- Force reload of updated embeddings
- Clear stale cache for testing 