#!/bin/bash

# Ensure OPENAI_API_KEY is set
if [ -z "$OPENAI_API_KEY" ]; then
  echo "Error: OPENAI_API_KEY environment variable is not set"
  echo "Please set it with: export OPENAI_API_KEY=your-key-here"
  exit 1
fi

# Install CDK if not already installed
if ! command -v cdk &> /dev/null; then
  echo "Installing AWS CDK..."
  npm install -g aws-cdk
fi

# Run the existing deployment script
./test_and_deploy.sh